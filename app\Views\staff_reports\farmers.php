<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Farmers Reports</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Farmers Reports</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Information -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['total_farmers']) ?></h4>
                            <p class="mb-0">Total Farmers</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['total_crop_blocks']) ?></h4>
                            <p class="mb-0">Crop Blocks</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-seedling"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['total_livestock_blocks']) ?></h4>
                            <p class="mb-0">Livestock Blocks</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-cow"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['farmers_with_email']) ?></h4>
                            <p class="mb-0">With Email</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>Farmers List
                    </h5>
                    <p class="card-text mb-0 text-muted"><?= count($farmers) ?> Total</p>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="farmersTable" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Farmer Code</th>
                                    <th>Name</th>
                                    <th>LLG</th>
                                    <th>Ward</th>
                                    <th>Contacts</th>
                                    <th>Email</th>
                                    <th>Crop Blocks</th>
                                    <th>Livestock Blocks</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($farmers as $farmer): ?>
                                    <tr>
                                        <td><strong><?= esc($farmer['farmer_code']) ?></strong></td>
                                        <td>
                                            <strong><?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></strong>
                                            <?php if (!empty($farmer['phone'])): ?>
                                                <br><small class="text-muted"><?= esc($farmer['phone']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= esc($farmer['llg_name'] ?? 'N/A') ?></td>
                                        <td><?= esc($farmer['ward_name'] ?? 'N/A') ?></td>
                                        <td>
                                            <?php if (!empty($farmer['phone'])): ?>
                                                <span class="badge bg-info"><?= esc($farmer['phone']) ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">No contact</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($farmer['email'])): ?>
                                                <?= esc($farmer['email']) ?>
                                            <?php else: ?>
                                                <span class="text-muted">No email</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-success"><?= $farmer['crop_blocks_count'] ?></span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-warning"><?= $farmer['livestock_blocks_count'] ?></span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group">
                                                <a href="<?= base_url('staff/reports/farmer_profile/' . $farmer['id']) ?>"
                                                   class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable with staff interface pattern
        $('#farmersTable').DataTable({
            responsive: true,
            order: [[0, 'desc']], // Sort by farmer code descending (newest first)
            pageLength: 25,
            columnDefs: [
                {
                    orderable: false,
                    targets: [8] // Actions column
                },
                {
                    className: 'text-center',
                    targets: [6, 7, 8] // Crop blocks, livestock blocks, actions columns
                }
            ],
            language: {
                search: "Search farmers:",
                lengthMenu: "Show _MENU_ farmers per page",
                info: "Showing _START_ to _END_ of _TOTAL_ farmers",
                infoEmpty: "No farmers found",
                infoFiltered: "(filtered from _MAX_ total farmers)"
            }
        });
    });
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
