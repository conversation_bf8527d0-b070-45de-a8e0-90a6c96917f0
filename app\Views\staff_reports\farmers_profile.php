<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0"><?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/reports/farmers') ?>">Farmers Report</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Farmer Profile</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/reports/farmers') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Farmers Report
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Farmer Profile Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>Farmer Information
                    </h5>
                    <p class="card-text mb-0 text-muted">Code: <?= esc($farmer['farmer_code']) ?></p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Farmer Code:</strong> <?= esc($farmer['farmer_code']) ?></p>
                                    <p class="mb-1"><strong>Gender:</strong> <?= esc($farmer['gender']) ?></p>
                                    <p class="mb-1"><strong>Age:</strong> <?= isset($farmer['date_of_birth']) ?
                                        date_diff(date_create($farmer['date_of_birth']), date_create('today'))->y : 'N/A' ?></p>
                                    <p class="mb-1"><strong>Children:</strong> <?= $farmer['children_count'] ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>District:</strong> <?= esc($farmer['district_name']) ?></p>
                                    <p class="mb-1"><strong>LLG:</strong> <?= esc($farmer['llg_name']) ?></p>
                                    <p class="mb-1"><strong>Ward:</strong> <?= esc($farmer['ward_name']) ?></p>
                                    <p class="mb-1"><strong>Village:</strong> <?= esc($farmer['village']) ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Quick Summary</h5>
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <div class="border rounded p-2 text-center">
                                                <h6 class="mb-1">Farm Blocks</h6>
                                                <span class="h4 text-primary"><?= $summaries['total_blocks'] ?></span>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="border rounded p-2 text-center">
                                                <h6 class="mb-1">Livestock Blocks</h6>
                                                <span class="h4 text-success"><?= $summaries['total_livestock_blocks'] ?></span>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="border rounded p-2 text-center">
                                                <h6 class="mb-1">Harvests</h6>
                                                <span class="h4 text-info"><?= $summaries['total_harvests'] ?></span>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="border rounded p-2 text-center">
                                                <h6 class="mb-1">Sales</h6>
                                                <span class="h4 text-warning"><?= $summaries['total_marketing'] ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Activities (Last 12 Months)
                    </h5>
                    <p class="card-text mb-0 text-muted">Latest farming activities and transactions</p>
                </div>
        <div class="card-body">
            <div class="timeline">
                <?php
                $activities = [];
                
                // Add harvests
                foreach ($harvests as $harvest) {
                    $activities[] = [
                        'date' => $harvest['harvest_date'],
                        'type' => 'harvest',
                        'data' => $harvest
                    ];
                }

                // Add marketing
                foreach ($marketing as $sale) {
                    $activities[] = [
                        'date' => $sale['market_date'],
                        'type' => 'marketing',
                        'data' => $sale
                    ];
                }

                // Add fertilizer applications
                foreach ($fertilizers as $fertilizer) {
                    $activities[] = [
                        'date' => $fertilizer['action_date'],
                        'type' => 'fertilizer',
                        'data' => $fertilizer
                    ];
                }

                // Add pesticide applications
                foreach ($pesticides as $pesticide) {
                    $activities[] = [
                        'date' => $pesticide['action_date'],
                        'type' => 'pesticide',
                        'data' => $pesticide
                    ];
                }

                // Add disease cases
                foreach ($diseases as $disease) {
                    $activities[] = [
                        'date' => $disease['action_date'],
                        'type' => 'disease',
                        'data' => $disease
                    ];
                }

                // Sort activities by date
                usort($activities, function($a, $b) {
                    return strtotime($b['date']) - strtotime($a['date']);
                });

                // Display activities
                foreach (array_slice($activities, 0, 10) as $activity): ?>
                    <div class="timeline-item">
                        <div class="timeline-date">
                            <?= date('M d, Y', strtotime($activity['date'])) ?>
                        </div>
                        <div class="timeline-content">
                            <?php switch($activity['type']):
                                case 'harvest': ?>
                                    <span class="badge bg-success">Harvest</span>
                                    Harvested <?= $activity['data']['quantity'] ?> <?= $activity['data']['unit_of_measure'] ?> 
                                    of <?= $activity['data']['crop_name'] ?> from block <?= $activity['data']['block_code'] ?>
                                    <?php break;
                                case 'marketing': ?>
                                    <span class="badge bg-primary">Sale</span>
                                    Sold <?= $activity['data']['quantity'] ?> <?= $activity['data']['unit_of_measure'] ?> 
                                    of <?= $activity['data']['crop_name'] ?> for K<?= number_format($activity['data']['market_price_per_unit'] * $activity['data']['quantity'], 2) ?>
                                    <?php break;
                                case 'fertilizer': ?>
                                    <span class="badge bg-info">Fertilizer</span>
                                    Applied <?= $activity['data']['quantity'] ?> <?= $activity['data']['unit_of_measure'] ?> 
                                    of <?= $activity['data']['fertilizer_name'] ?> to <?= $activity['data']['crop_name'] ?>
                                    <?php break;
                                case 'pesticide': ?>
                                    <span class="badge bg-warning">Pesticide</span>
                                    Applied <?= $activity['data']['quantity'] ?> <?= $activity['data']['unit_of_measure'] ?> 
                                    of <?= $activity['data']['pesticide_name'] ?> to <?= $activity['data']['crop_name'] ?>
                                    <?php break;
                                case 'disease': ?>
                                    <span class="badge bg-danger">Disease</span>
                                    Reported <?= $activity['data']['disease_name'] ?> affecting <?= $activity['data']['number_of_plants'] ?> 
                                    plants of <?= $activity['data']['crop_name'] ?>
                                    <?php break;
                            endswitch; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Farm Blocks -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>Farm Blocks
                    </h5>
                    <p class="card-text mb-0 text-muted"><?= count($farm_blocks) ?> Total</p>
                </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Block Code</th>
                            <th>Crop</th>
                            <th>Location</th>
                            <th>Site</th>
                            <th>Hectares</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($farm_blocks as $block): ?>
                            <tr>
                                <td><?= esc($block['block_code']) ?></td>
                                <td>
                                    <span class="badge" style="background-color: <?= esc($block['crop_color_code']) ?>">
                                        <?= esc($block['crop_name']) ?>
                                    </span>
                                </td>
                                <td><?= esc($block['village']) ?></td>
                                <td><?= esc($block['block_site']) ?></td>
                                <td><?= number_format($block['total_hectares'] ?? 0, 2) ?></td>
                                <td>
                                    <span class="badge bg-<?= $block['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($block['status']) ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Livestock -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-horse me-2"></i>Livestock</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Block Code</th>
                            <th>Livestock</th>
                            <th>Male</th>
                            <th>Female</th>
                            <th>Total</th>
                            <th>Last Updated</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($livestock as $animal): ?>
                            <tr>
                                <td><?= esc($animal['block_code']) ?></td>
                                <td>
                                    <span class="badge" style="background-color: <?= esc($animal['livestock_color_code']) ?>">
                                        <?= esc($animal['livestock_name']) ?>
                                    </span>
                                </td>
                                <td><?= $animal['he_total'] ?></td>
                                <td><?= $animal['she_total'] ?></td>
                                <td><?= $animal['he_total'] + $animal['she_total'] ?></td>
                                <td><?= date('M d, Y', strtotime($animal['action_date'])) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Marketing Summary -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Marketing Summary</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Crop</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Total</th>
                            <th>Buyer</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($marketing as $sale): ?>
                            <tr>
                                <td><?= date('M d, Y', strtotime($sale['market_date'])) ?></td>
                                <td><?= esc($sale['crop_name']) ?></td>
                                <td><?= $sale['quantity'] . ' ' . $sale['unit_of_measure'] ?></td>
                                <td>K<?= number_format($sale['market_price_per_unit'], 2) ?></td>
                                <td>K<?= number_format($sale['quantity'] * $sale['market_price_per_unit'], 2) ?></td>
                                <td><?= esc($sale['buyer_name']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding: 20px 0;
    border-left: 2px solid #e9ecef;
    margin-left: 20px;
}

.timeline-date {
    font-weight: bold;
    margin-bottom: 10px;
    margin-left: 20px;
}

.timeline-content {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-left: 20px;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -7px;
    top: 24px;
    width: 12px;
    height: 12px;
    background: #fff;
    border: 2px solid #007bff;
    border-radius: 50%;
}
</style>

<?= $this->endSection() ?> 